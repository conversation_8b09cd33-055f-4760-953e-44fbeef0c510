
export default {
    customerList: {
        position: {
            boss: '老板',
            purchase: '采购',
            finance: '财务',
            reception: '前台',
            repairman: '修理工',
        },
        deliveryAmountType: {
            free: '免运费',
            fixed: '固定运费',
            bargaining: '议价运费',
        },
        createModal: {
            titleAdd: '新增客户',
            titleEdit: '客户编辑',
        },
        button: {
            addCustomer: '新增客户',
            openMallAccount: '商城账号',
        },
        openMallAccount: {
            title: '开通客户商城账号',
            mallSecretKey: '商城密钥',
            secretKey: '密钥',
            boundDevice: '已绑设备',
            delete: '删除',
            addSecretKey: '新增密钥',
        },
        detailModal: {
            title: '客户详情',
        },
        table: {
            column: {
                customerSn: '客户编码',
                customerName: '客户名称',
                nickName: '客户简称',
                customerTags: '客户标签',
                storeName: '归属门店',
                salesmanName: '业务员',
                allowCredit: '支持挂账',
                creditLimit: '挂账额度',
                'creditLimit.used': '已用',
                'creditLimit.available': '/可用',
                creditTerms: '账期(天)',
                receivableAmount: '客户应收',
                advanceAmount: '客户预收',
                settleType: '结算类型',
                defaultContactName: '联系人',
                defaultContactPhone: '联系方式',
                defaultAddress: '客户地址',
                createTime: '创建时间',
                mallPermission: '已开通商城',
                ABN: 'ABN',
                status: '客户状态',
                universalEmail: '通用邮箱',
                Suburb: '区',
            },
            popconfirm: {
                enableDisable: '确认{operType}吗?',
            },
            search: {
                customerTags: '客户标签',
                store: '归属门店',
                customerInfo: '客户信息',
                'customerInfo.placeholder': '客户编码/客户名称/客户简称',
                contactName: '联系人',
                contactPhone: '联系方式',
                createTime: '创建时间',
            },
        },
        import: {
            taskDesc: '零售商客户导入',
        },
        export: {
            taskDesc: '零售商客户导出',
        },
        amountHistoryModal: {
            title: '额度调整记录',
            operatorLabel: '操作人',
            operationTypeLabel: '操作类型',
            operationContentLabel: '操作内容',
        },
        createForm: {
            group: {
                baseInfo: '基本信息',
                settlementInfo: '结算信息',
                contactInfo: '联系人信息',
                addressInfo: '地址信息',
            },
            allowOnAccount: '允许挂账',
            onAccountLimit: '挂账额度',
            customerExpectedAmount: '客户期望额度：${amount}',
            customerExpectedPeriod: '客户期望账期：{period}',
            accountPeriod: '账期',
            isMultiCurrency: '多币种客户',
            isGstExcluded: 'GST Excluded',
            companyEntity: '公司主体',
            simplifyCustomerId: 'Simplify客户ID',
            label: {
                customerName: '客户名称',
                customerSn: '客户编码',
                nickName: '客户简称',
                customerTags: '客户标签',
                store: '归属门店',
                salesman: '业务员',
                remark: '备注',
                customerPhoto: '客户照片',
                billingUnit: '开票单位',
                taxNo: '纳税识别号',
                bankName: '开户行名称',
                accountNo: '开户行账号',
                billingAddress: '开票地址',
                billingPhone: '开票电话',
                initialReceivable: '期初应收',
                allowCredit: '允许挂账',
                creditLimit: '挂账额度',
                creditTerms: '结算类型',
                // 通用邮箱
                universalEmail: '通用邮箱',
                // 财务邮箱
                financeEmail: '财务邮箱',
                // 运费类型
                deliverWay: '运费类型',
                // 发送财务邮件
                sendFinanceEmailFlag: '发送财务邮件',
                deliveryAmount: '运费',
            },
            placeholder: {
                deliveryAmount: '请输入运费',
            },
            tooltip: {
                creditLimit: '挂账额度',
                creditTerms: '结算类型',
            },
            button: {
                addContact: '新增联系人',
                addAddress: '新增地址',
            },
            contactTable: {
                column: {
                    isDefaultContact: '默认联系人',
                    contactName: '联系人',
                }
            },
            addressTable: {
                column: {
                    isDefaultAddress: '默认地址',
                    province: '洲',
                    prefecture: '区',
                    provinceCityDistrict: '洲/区',
                    operation: '操作',
                    detailAddress: '详细地址',
                    contactName: '联系人',
                    contactPhone: '联系人方式',
                    postCode: '邮编',
                },
            },
        },
        detailForm: {
            status: {
                enabled: '启用',
                disabled: '禁用',
            },
            label: {
                customerSn: '客户编码',
                nickName: '客户简称',
                customerTags: '客户标签',
                store: '归属门店',
                salesman: '业务员',
                createTime: '创建时间',
                remark: '客户备注',
                source: '客户来源',
            },
            group: {
                contactInfo: '联系人信息',
                addressInfo: '地址信息',
                settlementInfo: '结算信息',
            },
            tag: {
                defaultContact: '默认联系人',
                defaultAddress: '默认地址',
            },
            contact: {
                label: {
                    position: '职务',
                    phone: '联系方式',
                    email: '邮箱',
                    remark: '备注',
                    firstName: '名字',
                    lastName: '姓氏',
                },
            },
            address: {
                label: {
                    address: '地址',
                    phone: '联系方式',
                    area: '所在地区',
                    detailAddress: '详细地址',
                    contactName: '联系人',
                },
            },
            link: {
                viewAmountHistory: '查看额度调整记录',
            },
            settlement: {
                label: {
                    allowCredit: '支持挂账',
                    creditLimit: '挂账额度',
                    used: '已用额度',
                    frozen: '，冻结额度',
                    available: '，可用',
                    creditTerms: '结算类型',
                    usedAndFrozenCredit: '已用额度： {usedAmount}, 冻结额度： {freezeAmount}',
                    remainingCredit: '(已用 {usedAmount}，冻结 {freezeAmount}，可用 {availableAmount})',
                },
            },
            billing: {
                label: {
                    billingUnit: '开票单位：',
                    taxNo: '纳税识别号：',
                    bankName: '开户行名称：',
                    accountNo: '开户行账号：',
                    phone: '开票电话：',
                    address: '开票地址：',
                },
            },
        },
        contactTable: {
            column: {
                phone: '联系方式',
                position: '职务',
                email: '邮箱',
                remark: '备注',
                firstName: '名字',
                lastName: '姓氏',
            },
        },
    },
    customerProperty: {
        label: {
            customerTag: '客户标签',
            tag: '标签',
        },
        createModal: {
            titleAddTag: '新增标签',
            titleAddTagSuffix: '标签添加',
        },
        tab: {
            customerTag: '客户标签',
        },
        button: {
            addTag: '新增标签',
        },
        tagTable: {
            column: {
                tagName: '客户标签',
                source: '来源',
            },
            search: {
                tagName: '客户标签',
            },
        },
        priceLevel: {
            column: {
                name: '价格级别',
                source: '来源',
            },
        },
    },
};
