export default {
    customerList: {
        position: {
            boss: 'Boss',
            purchase: 'Purchase',
            finance: 'Finance',
            reception: 'Reception',
            repairman: 'Repairman',
        },
        deliveryAmountType: {
            free: 'Free',
            fixed: 'Fixed',
            bargaining: 'Bargaining',
        },
        createModal: {
            titleAdd: 'Add Customer',
            titleEdit: 'Edit Customer',
        },
        button: {
            addCustomer: 'Add Customer',
            openMallAccount: 'Mall Account',
        },
        openMallAccount: {
            title: 'Open Customer Mall Account',
            mallSecretKey: 'Mall Secret Key',
            secretKey: 'Secret Key',
            boundDevice: 'Bound Device',
            delete: 'Delete',
            addSecretKey: 'Add Secret Key',
        },
        detailModal: {
            title: 'Customer Details',
        },
        table: {
            column: {
                customerSn: 'Customer Code',
                customerName: 'Customer Name',
                nickName: 'Customer Nickname',
                customerTags: 'Customer Tags',
                storeName: 'Store',
                salesmanName: 'Salesperson',
                allowCredit: 'Allow Credit',
                creditLimit: 'Credit Limit',
                'creditLimit.used': 'Used',
                'creditLimit.available': '/Available',
                creditTerms: 'Credit Terms (days)',
                receivableAmount: 'Receivable Amount',
                advanceAmount: 'Advance Amount',
                settleType: 'Settle Type',
                defaultContactName: 'Contact',
                defaultContactPhone: 'Contact Phone',
                defaultAddress: 'Address',
                createTime: 'Creation Time',
                mallPermission: 'already open mall',
                ABN: 'ABN',
                status: 'Customer Status',
                universalEmail: 'Universal Email',
                Suburb: 'Suburb',
            },
            popconfirm: {
                enableDisable: 'Confirm {operType}?',
            },
            search: {
                customerTags: 'Customer Tags',
                store: 'Store',
                customerInfo: 'Customer Info',
                'customerInfo.placeholder': 'Customer Code/Name/Nickname',
                contactName: 'Contact Name',
                contactPhone: 'Contact Phone',
                createTime: 'Creation Time',
            },
        },
        import: {
            taskDesc: 'Retail Customer Import',
        },
        export: {
            taskDesc: 'Retail Customer Export',
        },
        amountHistoryModal: {
            title: 'Credit Adjustment History',
            operatorLabel: 'Operator',
            operationTypeLabel: 'Operation Type',
            operationContentLabel: 'Operation Content',
        },
        createForm: {
            group: {
                baseInfo: 'Basic Info',
                billingInfo: 'Billing Info',
                settlementInfo: 'Settlement Info',
                contactInfo: 'Contact Info',
                addressInfo: 'Address Info',
            },
            allowOnAccount: 'Allow on Account',
            onAccountLimit: 'On Account Limit',
            customerExpectedAmount: 'Customer Expected Amount: ${amount}',
            customerExpectedPeriod: 'Customer Expected Period: {period}',
            accountPeriod: 'Account Period',
            isMultiCurrency: 'Multi-currency Customer',
            isGstExcluded: 'GST Excluded',
            companyEntity: 'Company Entity',
            simplifyCustomerId: 'Simplify Customer ID',
            label: {
                customerName: 'Customer Name',
                customerSn: 'Customer Code',
                nickName: 'Customer Nickname',
                customerTags: 'Customer Tags',
                store: 'Store',
                salesman: 'Salesperson',
                remark: 'Remarks',
                customerPhoto: 'Customer Photo',
                billingUnit: 'Billing Unit',
                taxNo: 'Tax ID',
                bankName: 'Bank Name',
                accountNo: 'Bank Account',
                billingAddress: 'Billing Address',
                billingPhone: 'Billing Phone',
                initialReceivable: 'Initial Receivable',
                allowCredit: 'Allow Credit',
                creditLimit: 'Credit Limit',
                creditTerms: 'Credit Terms',
                // 通用邮箱
                universalEmail: 'Universal Email',
                // 财务邮箱
                financeEmail: 'Finance Email',
                // 运费类型
                deliverWay: 'Deliver Way',
                // 发送财务邮件 
                sendFinanceEmailFlag: 'Send Finance Email Flag',
                deliveryAmount: 'Delivery Amount',
            },
            placeholder: {
                deliveryAmount: 'Please enter delivery amount',
            },
            tooltip: {
                creditLimit: 'Credit Limit',
                creditTerms: 'Credit Terms',
            },
            button: {
                addContact: 'Add Contact',
                addAddress: 'Add Address',
            },
            contactTable: {
                column: {
                    isDefaultContact: 'Default Contact',
                    contactName: 'Contact Name',
                }
            },
            addressTable: {
                column: {
                    isDefaultAddress: 'Default Address',
                    province: 'State',
                    prefecture: 'Suburb',
                    provinceCityDistrict: 'State/Suburb',
                    operation: 'Operation',
                    detailAddress: 'Detailed Address',
                    contactName: 'Contact Name',
                    contactPhone: 'Contact Phone',
                    postCode: 'Post Code',
                },
            },
        },
        detailForm: {
            status: {
                enabled: 'Enabled',
                disabled: 'Disabled',
            },
            label: {
                customerSn: 'Customer Code',
                nickName: 'Customer Nickname',
                customerTags: 'Customer Tags',
                store: 'Store',
                salesman: 'Salesperson',
                createTime: 'Creation Time',
                remark: 'Customer Remarks',
                source: 'Customer Source',
            },
            group: {
                contactInfo: 'Contact Info',
                addressInfo: 'Address Info',
                settlementInfo: 'Settlement Info',
                billingInfo: 'Billing Info',
            },
            tag: {
                defaultContact: 'Default Contact',
                defaultAddress: 'Default Address',
            },
            contact: {
                label: {
                    position: 'Position',
                    phone: 'Phone',
                    email: 'Email',
                    remark: 'Remarks',
                    firstName: 'First Name',
                    lastName: 'Last Name',
                },
            },
            address: {
                label: {
                    address: 'Address',
                    phone: 'Phone',
                    area: 'Area',
                    detailAddress: 'Detailed Address',
                    contactName: 'Contact Name',
                },
            },
            link: {
                viewAmountHistory: 'View Credit Adjustment History',
            },
            settlement: {
                label: {
                    allowCredit: 'Allow Credit',
                    creditLimit: 'Credit Limit',
                    used: 'Used Limit',
                    frozen: ', Frozen Limit',
                    available: ', Available',
                    creditTerms: 'Credit Terms',
                    usedAndFrozenCredit: 'Used {usedAmount}, Frozen {freezeAmount}',
                    remainingCredit: '(Used {usedAmount}, Frozen {freezeAmount}, Available {availableAmount})',
                },
            },
            billing: {
                label: {
                    billingUnit: 'Billing Unit:',
                    taxNo: 'Tax ID:',
                    bankName: 'Bank Name:',
                    accountNo: 'Bank Account:',
                    phone: 'Billing Phone:',
                    address: 'Billing Address:',
                },
            },
        },
        contactTable: {
            column: {
                phone: 'Phone',
                position: 'Position',
                email: 'Email',
                remark: 'Remarks',
                firstName: 'First Name:',
                lastName: 'Last Name:',
            },
        },
    },
    customerProperty: {
        label: {
            customerTag: 'Customer Tag',
            tag: 'Tag',
        },
        createModal: {
            titleAddTag: 'Add Tag',
            titleAddTagSuffix: 'Tag Addition',
        },
        tab: {
            customerTag: 'Customer Tag',
        },
        button: {
            addTag: 'Add Tag',
        },
        tagTable: {
            column: {
                tagName: 'Customer Tag',
                source: 'Source',
            },
            search: {
                tagName: 'Customer Tag',
            },
        },
        priceLevel: {
            column: {
                name: 'Price Level',
                source: 'Source',
            },
        },
    },
};