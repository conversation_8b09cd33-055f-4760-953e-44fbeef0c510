import { getCstList } from '@/pages/customer/list/services';
import { MemberAccountEntity } from '@/pages/finance/customer/types/MemberAccountEntity';
import { orderStatusMap } from '@/pages/sales/order/list/types/OrderStatus';
import { payStatusMap } from '@/pages/sales/order/list/types/PayStatus';
import { paymentStatusMap } from '@/pages/sales/order/list/types/PaymentStatus';
import { queryWarehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/personnel/user/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { history } from '@umijs/max';
import type { OrderListItemEntity } from '../types/order.list.item.entity';
import { orderChannelOptions } from '@/pages/sales/order/edit/types/order.channel.code';
import { RemarkType } from '@/pages/sales/order/edit/types/update.order.remark.request';
import { Tag } from 'antd';

export interface OrderListColumns {
  operatorColumn: ProColumns<OrderListItemEntity>;
  activeTabKey: string;
  accountList: MemberAccountEntity[];
  intl: IntlShape;
}

export const tableColumns = (props: OrderListColumns) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderNo' }),
      dataIndex: ['orders', 'orderNo'],
      formItemProps: {
        name: 'orderNo',
      },
      fixed: 'left',
      width: 180,
      render: (text, record) => (
        <div>
          <a
            className="cursor-pointer"
            onClick={() => history.push(`/sales/order/detail?orderNo=${text}`)}
          >
            {text}
          </a>
          <div>
            {record?.orders?.orderTagList?.includes(2) && (
              <Tag color={'red'}>
                {props.intl.formatMessage({ id: 'sales.order.detail.urgency' })}
              </Tag>
            )}
          </div>
        </div>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.customerName' }),
      dataIndex: ['orders', 'cstName'],
      formItemProps: {
        name: 'cstId',
      },
      debounceTime: 200,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'cstName', value: 'cstId' },
      },
      request: () => getCstList({}),
      width: 180,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderTime' }),
      dataIndex: ['orders', 'orderCreateTime'],
      search: false,
      width: 180,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderTime' }),
      dataIndex: ['orders', 'orderCreateTime'],
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value: any) => {
          return {
            beginOrderTime: value[0],
            endOrderTime: value[1],
          };
        },
      },
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderAmount' }),
      dataIndex: ['orderPrice', 'shouldTotalOrderAmountYuan'],
      width: 80,
      search: false,
      valueType: 'money',
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.grossProfit' }),
      width: 80,
      dataIndex: ['orderPrice', 'grossProfitYuan'],
      valueType: 'money',
      search: false,
    },
    {
      title: 'Suburb',
      width: 80,
      dataIndex: ['orderFixedAddressList', 0, 'consigneePrefectureName'],
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.orderStatus' }),
      dataIndex: ['orderStatus', 'orderStatusName'],
      width: 80,
      valueType: 'select',
      valueEnum: orderStatusMap,
      formItemProps: {
        name: 'orderStatusList',
      },
      fieldProps: {
        disabled: props.activeTabKey,
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.settlementAccount' }),
      dataIndex: 'orderPayDetailList',
      width: 120,
      transform: (value) => {
        return { accountIds: value };
      },
      renderText: (orderPayDetailList) => {
        return orderPayDetailList
          ?.map((item) => (item.payKind == 2 ? item.payKindName : item.payeeAccountName))
          .join(',');
      },
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        name: 'accountIds',
        options: (props.accountList ?? [])?.map((item) => ({
          label: item.memberAccountName,
          value: item.id,
        })),
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.settlementStatus' }),
      dataIndex: ['orderStatus', 'payStatus'],
      width: 80,
      valueType: 'select',
      valueEnum: payStatusMap,
      formItemProps: {
        name: 'payStatusList',
      },
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.paymentStatus' }),
      dataIndex: ['orderStatus', 'paymentStatus'],
      width: 80,
      valueType: 'select',
      valueEnum: paymentStatusMap,
      formItemProps: {
        name: 'paymentStatusList',
      },
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.payTime' }),
      dataIndex: ['orderPayList', 0, 'payTime'],
      width: 180,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.outboundTime' }),
      dataIndex: ['orderFixedDistributionList', 0, 'outboundFinishTime'],
      width: 180,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.finishTime' }),
      dataIndex: ['orders', 'orderFinishTime'],
      width: 180,
      search: false,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.productInfo' }),
      dataIndex: 'itemInfo',
      hideInTable: true,
      fieldProps: {
        placeholder: props.intl.formatMessage({ id: 'sales.order.list.productSearchPlaceholder' }),
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.salesStore' }),
      dataIndex: ['orders', 'storeName'],
      width: 120,
      debounceTime: 300,
      valueType: 'select',
      ellipsis: true,
      formItemProps: {
        name: 'storeIdList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
      },
      request: (query) =>
        queryStoreByAccount({}).then((result) =>
          result.map((item) => ({ label: item.name, value: item.id })),
        ),
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.deliveryWarehouse' }),
      dataIndex: ['orderFixedDistributionList', 0, 'warehouseName'],
      width: 120,
      ellipsis: true,
      debounceTime: 300,
      valueType: 'select',
      formItemProps: {
        name: 'deliWarehouseIdList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
      },
      request: (query) =>
        queryWarehouseList({
          warehouseNameKeyword: query.keyWords,
          pageSize: 99,
        }).then((result) =>
          result.data.map((item) => ({ label: item.warehouseName, value: item.id })),
        ),
    },
    {
      title: '订单来源',
      dataIndex: ['orders', 'channelName'],
      width: 80,
      formItemProps: {
        name: 'orderChannelList',
      },
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: orderChannelOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.list.creator' }),
      dataIndex: ['orders', 'createPerson'],
      search: {
        transform: (value: any) => {
          return {
            createPersonIdList: [value],
          };
        },
      },
      width: 60,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.remark' }),
      width: 80,
      ellipsis: true,
      hideInTable: true,
      formItemProps: {
        name: 'remark',
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.edit.saleRemark' }),
      width: 80,
      ellipsis: true,
      hideInSearch: true,
      render: (_, record) => {
        return record?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToCustomer)
          ?.noteDetail;
      },
    },
    {
      title: props.intl.formatMessage({ id: 'sales.order.edit.innerRemark' }),
      width: 80,
      hideInSearch: true,
      ellipsis: true,
      render: (_, record) => {
        return record?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToInner)
          ?.noteDetail;
      },
    },
    props.operatorColumn,
  ] as ProColumns<OrderListItemEntity>[];
