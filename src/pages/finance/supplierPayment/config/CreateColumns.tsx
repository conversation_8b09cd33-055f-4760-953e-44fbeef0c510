import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { ProFormDigit, type ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { MoneyFormat } from '../../receive';

export interface CreateReceivedOrderDetailColumnsProps {
  handleUpdate: (record: FinPayableEntity) => void;
  intl: IntlShape;
}

export default (props: CreateReceivedOrderDetailColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
      dataIndex: 'sellerName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.businessOrderNo' }),
      dataIndex: 'orderNo',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.businessCompleteTime' }),
      dataIndex: 'billDate',
      width: 140,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'common.field.currency' }),
      dataIndex: 'currency',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'common.field.rate' }),
      dataIndex: 'rate',
      search: false,
      width: 80,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.orderAmount' }),
      dataIndex: 'orderAmountYuan',
      search: false,
      width: 100,
      editable: false,
      render: (text) => <MoneyFormat money={text} />,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.paidAmount' }),
      dataIndex: 'paymentAmountYuan',
      search: false,
      width: 100,
      render: (text) => <MoneyFormat money={text} />,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.unpaidAmount' }),
      dataIndex: 'remainPayableAmountYuan',
      search: false,
      width: 100,
      render: (text) => <MoneyFormat money={text} />,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.currentWriteOff' }),
      dataIndex: 'currPayAmount',
      search: false,
      width: 100,
      editable: false,
      render: (text, record) => {
        return (
          <ProFormDigit
            placeholder={props.intl.formatMessage({ id: 'finance.supplierPayment.placeholders.enterAmount' })}
            max={999999999.99}
            min={-999999999.99}
            fieldProps={{
              controls: false,
              precision: 2,
              value: record?.currPayAmount,
              onChange: (value) => props.handleUpdate({ ...record, currPayAmount: value }),
            }}
          />
        );
      },
    },
  ] as ProColumns<FinPayableEntity>[];
