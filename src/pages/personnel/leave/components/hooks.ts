
import { useBoolean } from 'ahooks';
import { useRef, useState } from 'react';
import { LeaveEntity } from './types/leave.entity';
import { LeaveExchangeChannel } from './types/leave.enum';

export const useLeavePage = () => {
  const [applyLeaveModalVisible, { setTrue: showApplyLeaveModal, setFalse: hideApplyLeaveModal }] = useBoolean(false);
  const [payLeaveModalVisible, { setTrue: showPayLeaveModal, setFalse: hidePayLeaveModal }] = useBoolean(false);
  const [approveLeaveModalVisible, { setTrue: showApproveLeaveModal, setFalse: hideApproveLeaveModal }] = useBoolean(false);

  const [selectedLeave, setSelectedLeave] = useState<any>(null);

  const actionRef = useRef<any>();

  const handleApplyLeave = () => {
    setSelectedLeave(null);
    showApplyLeaveModal();
  };

  const handleEditApplyLeave = (leave: LeaveEntity) => {
    if (leave.exchangeChannel === LeaveExchangeChannel.Leave) {
      showApplyLeaveModal();
    }
    if (leave.exchangeChannel === LeaveExchangeChannel.Pay_Leave) {
      showPayLeaveModal();
    }
    setSelectedLeave(leave);
  };

  const handlePayLeave = () => {
    showPayLeaveModal();
  };

  const handleApproveLeave = (record: any) => {
    setSelectedLeave(record);
    showApproveLeaveModal();
  };

  const reload = () => {
    actionRef.current?.reload();
  }

  return {
    actionRef,
    applyLeaveModalVisible,
    payLeaveModalVisible,
    approveLeaveModalVisible,
    selectedLeave,
    handleApplyLeave,
    handlePayLeave,
    handleApproveLeave,
    hideApplyLeaveModal,
    hidePayLeaveModal,
    hideApproveLeaveModal,
    handleEditApplyLeave,
    reload,
  };
};
